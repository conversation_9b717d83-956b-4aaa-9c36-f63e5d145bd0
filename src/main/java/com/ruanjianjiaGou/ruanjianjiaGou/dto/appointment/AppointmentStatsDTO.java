package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 预约统计信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentStatsDTO {
    
    /**
     * 总预约数
     */
    private Long totalAppointments;
    
    /**
     * 待就诊预约数
     */
    private Long bookedAppointments;
    
    /**
     * 已完成预约数
     */
    private Long completedAppointments;
    
    /**
     * 已取消预约数
     */
    private Long cancelledAppointments;
    
    /**
     * 即将到来的预约数（7天内）
     */
    private Long upcomingAppointments;
    
    /**
     * 本月预约数
     */
    private Long thisMonthAppointments;
    
    /**
     * 最近预约的医生姓名
     */
    private String lastDoctorName;
    
    /**
     * 最近预约的科室名称
     */
    private String lastDepartmentName;
    
    /**
     * 预约完成率（百分比）
     */
    private Double completionRate;
    
    /**
     * 取消率（百分比）
     */
    private Double cancellationRate;
}
