package com.ruanjianjiaGou.ruanjianjiaGou.dto.appointment;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 检查预约冲突DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckConflictDTO {
    
    /**
     * 预约日期
     */
    @NotNull(message = "预约日期不能为空")
    private LocalDate appointmentDate;
    
    /**
     * 预约时间
     */
    @NotNull(message = "预约时间不能为空")
    private LocalTime appointmentTime;
    
    /**
     * 健康档案ID
     */
    @NotNull(message = "健康档案ID不能为空")
    private Long profileId;
    
    /**
     * 排班ID（可选，用于检查特定排班冲突）
     */
    private Long scheduleId;
    
    /**
     * 医生ID（可选，用于检查同一医生的冲突）
     */
    private Long doctorId;
    
    /**
     * 科室ID（可选，用于检查同一科室的冲突）
     */
    private Long departmentId;
}
